# 更新记录

V1.6.1 - 2016-12-03

- 解决了有道词典调用限制的问题
- 解决翻译中的浮层无法关闭的问题

V1.6 - 2016-06-17

- 支持必应翻译

V1.5.4 - 2016-05-28

- 解决链接划词模式劫持全局快捷键导致页面链接点击异常的问题
- 链接划词的快捷键更新为 Ctrl+Shift+L

V1.5.3 - 2016-05-26

- 解决有时页面冒出一大堆之前的翻译结果的问题 #58

V1.5.1 - 2016-03-06

- 解决开启链接划词模式会破坏某些网页布局的问题

V1.5 - 2015-12-27

- 使用黑科技重新实现百度词典翻译

V1.4.2 - 2015-12-19

- 百度词典API已经停止服务，移除百度翻译

V1.4 - 2015-12-19

- 可以手动关闭页面划词翻译的结果面板
- 扩展更新时显示功能更新

V1.3 - 2015-03-11

- 添加独立的偏好设定页面，并精简弹出窗口
- 鼠标移上页面翻译结果时，结果面板不消失，移出后重新计时
- 解决页面翻译结果被页内查找框遮盖的问题
- 页面划词翻译结果分为两种显示形式，就近和窗口边缘
- 将链接划词的激活快捷键修改为敲击两次 Caps Lock
- 弹出窗口中支持长文本的翻译
- 添加百度翻译的服务，可以在有道和百度之间切换了

V1.2 - 2014-05-30

- 支持对 IFRAME 中的内容进行划词翻译
- 支持对使用了 Turbolinks 技术的网站进行划词翻译
- 支持在划词内容附近显示翻译结果
- 限于有道翻译 API 的使用协议，移除缓存功能

V1.1 - 2014-02-16

- 页面划词时，如果一个单词的翻译还未消失，再次对该词划词，不会重复翻译
- 修正部分单词没有查找到翻译仍然显示翻译结果的问题
- 页面划词翻译结果增加透明和淡入淡出效果
- 为翻译结果添加音标（如果有）
- 更新链接文本划词翻译的描述
- 解决频繁划词导致翻译结果跑出屏幕的问题（支持使用滚轮滚动）
