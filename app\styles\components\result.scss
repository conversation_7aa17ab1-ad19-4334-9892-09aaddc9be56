#cst-list .cst-result,
.cst-result {
  padding: 3px 5px;

  .cst-result-phonetic {
    margin: 0 0 5px 0;
  }

  h6, code, pre {
    border: none;
    background: none;
    color: inherit;
    padding: 0;
    margin: 0;
    text-transform: none;
    font-size: 14px;
    line-height: 20px;
    white-space: normal;
  }

  .cst-result-text {
    margin-bottom: 5px;
    font-weight: 600;
  }

  .cst-result-phonetic {
    padding-top: 5px !important;
    padding-bottom: 10px !important;
    color: #58afb1;
  }

  .cst-result-translation {
    overflow-y: auto;
    max-height: 200px;

    .additional {
      color: #aaa !important;
      font-size: 0.9em !important;
      margin-top: 5px !important;
      padding-bottom: 5px !important;
    }
  }
}

.cst-result[data-cst-theme="dark"] {
  &[data-cst-status="success"],
  &[data-cst-status="pending"] {
    background: #336721;
    color: #EDF8ED;
  }

  &[data-cst-status="failure"] {
    background: #FFF299;
    color: #888888;
  }
}

.cst-result[data-cst-theme="light"] {
  &[data-cst-status="success"],
  &[data-cst-status="pending"] {
    background: #DDEADD;
    color: #2B3F29;
  }

  &[data-cst-status="failure"] {
    background: #fff3c8;
    color: #888888;
  }
}
