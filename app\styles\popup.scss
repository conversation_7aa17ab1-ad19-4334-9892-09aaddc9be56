$font-color: #555;

@import 'mixins/reset';
@import 'components/loader';
@import 'components/result';

body {
  width: 240px;
  min-height: 85px;
  margin: 0;
  font-size: 14px;
  color: $font-color;
  background-color: #F2F5F6;
}

[v-cloak] {
  visibility: hidden;
}

.translator {
  .input-box {
    padding: 5px 5px 2px 5px;;

    textarea {
      -webkit-appearance: textfield;
      border: 1px inset #e0e0e0;
      background-color: #fefbf5;
      resize: none;
      font-size: 12px;
      line-height: 1.2em;
      color: #888;
      width: 100%;
      margin: 0;
      font-weight: bold;
      box-sizing: border-box;

      &:active, &:focus {
        outline: none;
        background-color: #ffffd4;
      }
    }
  }

  .result-wrapper {
    @include reset;
    padding: 0px 5px !important;

    .cst-result {
      padding: 0 5px;
    }
  }

  footer {
    height: 24px;
    line-height: 24px;
    padding: 0 5px;

    .btn-settings {
      float: right;

      .fa-icon {
        fill: $font-color;
        margin-top: 5px;
        vertical-align: top;
        width: auto;
        height: 1em;
        max-width: 100%;
        max-height: 100%;
      }
    }

    label {
      .site {
        font-style: italic;
        font-weight: bold;
        font-size: .9em;
        display: inline-block;
        vertical-align: bottom;
        max-width: 50px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}

label {
  font-size: 0.9em;
  color: gray;
  user-select: none;

  &.enabled {
    color: green;
  }

  input[type="checkbox"] {
    margin: 0;
  }
}
