{"name": "smooth-translator", "private": true, "version": "0.2.0", "scripts": {"start": "npm run dev:chrome", "build": "npm run build:chrome", "build:chrome": "gulp pack --production --vendor=chrome", "build:firefox": "gulp pack --production --vendor=firefox", "build:opera": "gulp pack --production --vendor=opera", "build:edge": "gulp pack --production --vendor=edge", "dev": "npm run dev:chrome", "dev:chrome": "gulp --watch --vendor=chrome", "dev:firefox": "gulp --watch --vendor=firefox", "dev:opera": "gulp --watch --vendor=opera", "dev:edge": "gulp --watch --vendor=edge", "lint": "standard"}, "standard": {"globals": ["chrome"]}, "devDependencies": {"babel-cli": "^6.26.0", "babel-core": "^6.26.3", "babel-loader": "7.x.x", "babel-preset-env": "^1.7.0", "chai": "4.x.x", "chromereload": "0.x.x", "debounce": "1.x.x", "del": "3.x.x", "gulp": "3.x.x", "gulp-bump": "2.x.x", "gulp-cache": "0.x.x", "gulp-clean-css": "^3.x.x", "gulp-filter": "^5.x.x", "gulp-git": "^2.x.x", "gulp-if": "2.x.x", "gulp-imagemin": "3.x.x", "gulp-json-transform": "0.x.x", "gulp-less": "3.x.x", "gulp-livereload": "3.x.x", "gulp-plumber": "1.x.x", "gulp-sass": "^3.x.x", "gulp-sequence": "0.x.x", "gulp-sourcemaps": "^2.x.x", "gulp-tag-version": "1.x.x", "gulp-util": "3.x.x", "gulp-zip": "^4.x.x", "require-dir": "1.x.x", "standard": "^10.0.2", "vinyl-named": "1.x.x", "vue-loader": "^13.3.0", "vue-template-compiler": "^2.5.2", "webpack": "3.x.x", "webpack-stream": "3.x.x", "yargs": "^8.x.x"}, "dependencies": {"babel-preset-es2015": "^6.24.1", "chrome-storage-wrapper": "^0.1.4", "css-loader": "^1.0.0", "jquery": "^3.3.1", "lodash": "^4.17.10", "lscache": "^1.1.0", "url-parse": "^1.4.3", "vue": "^2.5.2", "vue-awesome": "^2.3.3", "wait-until-promise": "^1.0.0"}}